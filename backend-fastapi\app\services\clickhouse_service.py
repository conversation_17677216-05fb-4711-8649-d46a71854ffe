"""
ClickHouse Database Service
Provides connection and query operations for ClickHouse database
"""
import clickhouse_connect
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, date
import pandas as pd
from app.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ClickHouseService:
    """Service for ClickHouse database operations"""
    
    def __init__(self, host: str = None, port: int = None,
                 username: str = None, password: str = None,
                 database: str = None, secure: bool = None, verify: bool = None):
        """
        Initialize ClickHouse connection using settings from config

        Args:
            host: ClickHouse server host (default: from settings)
            port: ClickHouse server port (default: from settings)
            username: Database username (default: from settings)
            password: Database password (default: from settings)
            database: Database name (default: from settings)
            secure: Use secure connection (default: from settings)
            verify: Verify SSL certificates (default: from settings)
        """
        self.host = host or settings.db_host
        self.port = port or settings.db_port
        self.username = username or settings.db_user
        self.password = password or settings.db_password
        self.database = database or settings.db_name
        self.secure = secure if secure is not None else settings.db_secure
        self.verify = verify if verify is not None else settings.db_verify
        self._client = None
    
    def get_client(self):
        """Get or create ClickHouse client connection"""
        if self._client is None:
            try:
                # Connection parameters
                connect_params = {
                    'host': self.host,
                    'port': self.port,
                    'username': self.username,
                    'password': self.password,
                    'database': self.database,
                    'secure': self.secure,
                    'verify': self.verify,
                }

                # Add SSL certificate parameters if provided
                if settings.db_ca_cert:
                    connect_params['ca_cert'] = settings.db_ca_cert
                if settings.db_client_cert:
                    connect_params['client_cert'] = settings.db_client_cert
                if settings.db_client_key:
                    connect_params['client_key'] = settings.db_client_key

                self._client = clickhouse_connect.get_client(**connect_params)
                logger.info(f"Connected to ClickHouse at {self.host}:{self.port}")
            except Exception as e:
                logger.error(f"Failed to connect to ClickHouse: {str(e)}")
                raise
        return self._client
    
    def execute_query(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Execute a query and return results as list of dictionaries
        
        Args:
            query: SQL query string
            parameters: Query parameters for parameterized queries
            
        Returns:
            List of dictionaries representing query results
        """
        try:
            client = self.get_client()
            
            if parameters:
                result = client.query(query, parameters=parameters)
            else:
                result = client.query(query)
            
            # Convert result to list of dictionaries
            columns = result.column_names
            rows = result.result_rows
            
            return [dict(zip(columns, row)) for row in rows]
            
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            logger.error(f"Query: {query}")
            if parameters:
                logger.error(f"Parameters: {parameters}")
            raise
    
    def execute_query_df(self, query: str, parameters: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        Execute a query and return results as pandas DataFrame
        
        Args:
            query: SQL query string
            parameters: Query parameters for parameterized queries
            
        Returns:
            pandas DataFrame with query results
        """
        try:
            client = self.get_client()
            
            if parameters:
                result = client.query_df(query, parameters=parameters)
            else:
                result = client.query_df(query)
            
            return result
            
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            logger.error(f"Query: {query}")
            if parameters:
                logger.error(f"Parameters: {parameters}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test the ClickHouse connection
        
        Returns:
            True if connection is successful, False otherwise
        """
        try:
            client = self.get_client()
            result = client.query("SELECT 1 as test")
            return len(result.result_rows) > 0
        except Exception as e:
            logger.error(f"Connection test failed: {str(e)}")
            return False
    
    def close(self):
        """Close the ClickHouse connection"""
        if self._client:
            try:
                self._client.close()
                self._client = None
                logger.info("ClickHouse connection closed")
            except Exception as e:
                logger.error(f"Error closing connection: {str(e)}")

# Global ClickHouse service instance - initialized lazily
clickhouse_service = None

def get_clickhouse_service():
    """Get or create the global ClickHouse service instance"""
    global clickhouse_service
    if clickhouse_service is None:
        clickhouse_service = ClickHouseService()
    return clickhouse_service
