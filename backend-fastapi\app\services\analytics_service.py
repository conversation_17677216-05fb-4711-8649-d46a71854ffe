from typing import List, Dict, Set
import logging
from collections import defaultdict
from app.models import SymptomCluster, ClinicalDataQuery, PatientDemographics, QualityMetrics
from app.services.clinical_service import ClinicalService

logger = logging.getLogger(__name__)

class AnalyticsService:
    
    @staticmethod
    def get_symptom_clusters() -> List[SymptomCluster]:
        """Generate symptom clusters and probable diagnoses"""
        try:
            # Get clinical data for analysis (use max allowed limit)
            query = ClinicalDataQuery(limit=1000)
            clinical_data = ClinicalService.get_clinical_data(query)
            visits = clinical_data.visits
            
            # Group symptoms by diagnosis
            diagnosis_symptom_map: Dict[str, Set[str]] = defaultdict(set)
            
            for visit in visits:
                if visit.diagnosis and visit.chiefComplaint:
                    diagnosis_symptom_map[visit.diagnosis].add(visit.chiefComplaint)
            
            # Create symptom clusters based on common patterns
            clusters = []
            
            # Respiratory symptoms cluster
            respiratory_symptoms = set()
            respiratory_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'cough', 'breath', 'chest', 'fever', 'cold', 'flu', 
                        'respiratory', 'pneumonia', 'bronchitis', 'asthma'
                    ]):
                        respiratory_symptoms.add(symptom)
                        respiratory_diagnoses.add(diagnosis)
            
            if respiratory_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Respiratory Symptoms",
                    symptoms=list(respiratory_symptoms)[:10],  # Limit to 10 symptoms
                    probableDiagnosis=', '.join(list(respiratory_diagnoses)[:5])  # Limit to 5 diagnoses
                ))
            
            # Cardiovascular symptoms cluster
            cardio_symptoms = set()
            cardio_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'chest pain', 'heart', 'palpitation', 'hypertension', 
                        'blood pressure', 'cardiac', 'angina', 'arrhythmia'
                    ]):
                        cardio_symptoms.add(symptom)
                        cardio_diagnoses.add(diagnosis)
            
            if cardio_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Cardiovascular Symptoms",
                    symptoms=list(cardio_symptoms)[:10],
                    probableDiagnosis=', '.join(list(cardio_diagnoses)[:5])
                ))
            
            # Gastrointestinal symptoms cluster
            gi_symptoms = set()
            gi_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'abdominal', 'stomach', 'nausea', 'vomit', 'diarrhea', 
                        'constipation', 'gastro', 'bowel', 'digestive'
                    ]):
                        gi_symptoms.add(symptom)
                        gi_diagnoses.add(diagnosis)
            
            if gi_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Gastrointestinal Symptoms",
                    symptoms=list(gi_symptoms)[:10],
                    probableDiagnosis=', '.join(list(gi_diagnoses)[:5])
                ))
            
            # Neurological symptoms cluster
            neuro_symptoms = set()
            neuro_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'headache', 'dizz', 'memory', 'vision', 'neurolog', 
                        'migraine', 'seizure', 'stroke', 'confusion'
                    ]):
                        neuro_symptoms.add(symptom)
                        neuro_diagnoses.add(diagnosis)
            
            if neuro_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Neurological Symptoms",
                    symptoms=list(neuro_symptoms)[:10],
                    probableDiagnosis=', '.join(list(neuro_diagnoses)[:5])
                ))
            
            # Musculoskeletal symptoms cluster
            musculo_symptoms = set()
            musculo_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'pain', 'back', 'joint', 'muscle', 'arthritis', 
                        'fracture', 'sprain', 'orthopedic', 'bone'
                    ]):
                        musculo_symptoms.add(symptom)
                        musculo_diagnoses.add(diagnosis)
            
            if musculo_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Musculoskeletal Symptoms",
                    symptoms=list(musculo_symptoms)[:10],
                    probableDiagnosis=', '.join(list(musculo_diagnoses)[:5])
                ))
            
            # Metabolic symptoms cluster
            meta_symptoms = set()
            meta_diagnoses = set()
            
            for diagnosis, symptoms in diagnosis_symptom_map.items():
                for symptom in symptoms:
                    symptom_lower = symptom.lower()
                    if any(keyword in symptom_lower for keyword in [
                        'diabetes', 'blood sugar', 'thyroid', 'weight', 
                        'metabolic', 'hormone', 'endocrine', 'fatigue'
                    ]):
                        meta_symptoms.add(symptom)
                        meta_diagnoses.add(diagnosis)
            
            if meta_symptoms:
                clusters.append(SymptomCluster(
                    clusterName="Metabolic Symptoms",
                    symptoms=list(meta_symptoms)[:10],
                    probableDiagnosis=', '.join(list(meta_diagnoses)[:5])
                ))
            
            # If no clusters were generated, return some default clusters
            if not clusters:
                clusters = [
                    SymptomCluster(
                        clusterName="General Symptoms",
                        symptoms=["Fever", "Fatigue", "Pain", "Headache"],
                        probableDiagnosis="Common conditions requiring evaluation"
                    )
                ]
            
            return clusters
            
        except Exception as error:
            logger.error(f'Error generating symptom clusters: {error}')
            # Return fallback clusters in case of error
            return [
                SymptomCluster(
                    clusterName="Respiratory Symptoms",
                    symptoms=["Cough", "Shortness of breath", "Chest pain", "Fever"],
                    probableDiagnosis="URTI, Bronchitis, Pneumonia"
                ),
                SymptomCluster(
                    clusterName="Cardiovascular Symptoms",
                    symptoms=["Chest pain", "Palpitations", "Leg swelling", "Dizziness"],
                    probableDiagnosis="Heart Failure, Atrial Fibrillation, Hypertension"
                ),
                SymptomCluster(
                    clusterName="Gastrointestinal Symptoms",
                    symptoms=["Abdominal Pain", "Nausea", "Vomiting", "Diarrhea"],
                    probableDiagnosis="Gastroenteritis, IBS, GERD"
                ),
                SymptomCluster(
                    clusterName="Neurological Symptoms",
                    symptoms=["Headache", "Dizziness", "Memory problems", "Blurred vision"],
                    probableDiagnosis="Migraine, Stroke, Hypertension"
                )
            ]

    @staticmethod
    def get_patient_demographics() -> PatientDemographics:
        """Get patient demographic information"""
        try:
            # Get clinical data for analysis (use max allowed limit)
            query = ClinicalDataQuery(limit=1000)
            clinical_data = ClinicalService.get_clinical_data(query)
            visits = clinical_data.visits

            # Get unique patients
            unique_patients = {}
            for visit in visits:
                if visit.patient.id not in unique_patients:
                    unique_patients[visit.patient.id] = visit.patient

            patients = list(unique_patients.values())
            total_patients = len(patients)

            # Age group distribution
            age_groups = {
                '0-18': 0,
                '19-35': 0,
                '36-50': 0,
                '51-65': 0,
                '65+': 0
            }

            for patient in patients:
                age = patient.age
                if age <= 18:
                    age_groups['0-18'] += 1
                elif age <= 35:
                    age_groups['19-35'] += 1
                elif age <= 50:
                    age_groups['36-50'] += 1
                elif age <= 65:
                    age_groups['51-65'] += 1
                else:
                    age_groups['65+'] += 1

            # Gender distribution
            gender_distribution = {}
            for patient in patients:
                gender = patient.gender.value
                gender_distribution[gender] = gender_distribution.get(gender, 0) + 1

            return PatientDemographics(
                totalPatients=total_patients,
                ageGroups=age_groups,
                genderDistribution=gender_distribution
            )

        except Exception as error:
            logger.error(f'Error getting patient demographics: {error}')
            # Return fallback data
            return PatientDemographics(
                totalPatients=0,
                ageGroups={'0-18': 0, '19-35': 0, '36-50': 0, '51-65': 0, '65+': 0},
                genderDistribution={'Male': 0, 'Female': 0, 'Other': 0}
            )

    @staticmethod
    def get_quality_metrics() -> QualityMetrics:
        """Get quality metrics and performance indicators"""
        try:
            # Get clinical data for analysis (use max allowed limit)
            query = ClinicalDataQuery(limit=1000)
            clinical_data = ClinicalService.get_clinical_data(query)
            visits = clinical_data.visits

            if not visits:
                return QualityMetrics(
                    totalPatients=0,
                    referralRate=0.0,
                    polypharmacyCount=0,
                    avgMedicationsPerVisit=0.0,
                    avgInvestigationsPerVisit=0.0,
                    mostCommonDiagnosis='N/A',
                    mostCommonComplaint='N/A'
                )

            # Get unique patients
            unique_patients = set(visit.patient.id for visit in visits)
            total_patients = len(unique_patients)

            # Calculate referral rate
            referral_count = sum(1 for visit in visits if visit.referred)
            referral_rate = (referral_count / len(visits)) * 100 if visits else 0

            # Calculate polypharmacy count (5+ medications)
            polypharmacy_count = sum(1 for visit in visits if len(visit.medications) >= 5)

            # Calculate average medications per visit
            total_medications = sum(len(visit.medications) for visit in visits)
            avg_medications_per_visit = total_medications / len(visits) if visits else 0

            # Calculate average investigations per visit
            total_investigations = sum(len(visit.investigations) for visit in visits)
            avg_investigations_per_visit = total_investigations / len(visits) if visits else 0

            # Most common diagnosis
            diagnosis_counts = {}
            for visit in visits:
                diagnosis = visit.diagnosis
                diagnosis_counts[diagnosis] = diagnosis_counts.get(diagnosis, 0) + 1

            most_common_diagnosis = 'N/A'
            if diagnosis_counts:
                most_common_diagnosis = max(diagnosis_counts, key=diagnosis_counts.get)

            # Most common complaint
            complaint_counts = {}
            for visit in visits:
                complaint = visit.chiefComplaint
                complaint_counts[complaint] = complaint_counts.get(complaint, 0) + 1

            most_common_complaint = 'N/A'
            if complaint_counts:
                most_common_complaint = max(complaint_counts, key=complaint_counts.get)

            return QualityMetrics(
                totalPatients=total_patients,
                referralRate=round(referral_rate, 2),
                polypharmacyCount=polypharmacy_count,
                avgMedicationsPerVisit=round(avg_medications_per_visit, 2),
                avgInvestigationsPerVisit=round(avg_investigations_per_visit, 2),
                mostCommonDiagnosis=most_common_diagnosis,
                mostCommonComplaint=most_common_complaint
            )

        except Exception as error:
            logger.error(f'Error getting quality metrics: {error}')
            # Return fallback data
            return QualityMetrics(
                totalPatients=0,
                referralRate=0.0,
                polypharmacyCount=0,
                avgMedicationsPerVisit=0.0,
                avgInvestigationsPerVisit=0.0,
                mostCommonDiagnosis='N/A',
                mostCommonComplaint='N/A'
            )
