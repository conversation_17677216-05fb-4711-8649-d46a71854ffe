"""
Revenue Service for Monthly Revenue Summary
Implements queries for MTD, YTD, YoY analysis using ClickHouse
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import logging
from app.services.clickhouse_service import get_clickhouse_service
from app.models import (
    RevenueFilterParams, MonthlyTrendResponse, DailyTrendResponse,
    MTDComparisonResponse, YTDComparisonResponse, YoYComparisonResponse,
    DoctorSummaryResponse, ServiceSummaryResponse, MonthlySummaryResponse
)

logger = logging.getLogger(__name__)

class RevenueService:
    """Service for revenue analytics and monthly summary operations"""
    
    @staticmethod
    def get_monthly_trend(unitid: str, trantype: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get monthly trend data
        
        Args:
            unitid: Unit ID to filter data
            trantype: Optional transaction type filter
            
        Returns:
            List of monthly trend data
        """
        query = """
        SELECT
            DATE_TRUNC('month', billdate) AS month,
            trantype,
            COUNT(DISTINCT billno) AS bill_count,
            SUM(netamount) AS total_netamount
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if trantype:
            query += " AND trantype = {trantype:String}"
            parameters["trantype"] = trantype
            
        query += """
        GROUP BY 1, 2
        ORDER BY 1, 2
        """
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_monthly_trend: {str(e)}")
            raise
    
    @staticmethod
    def get_daily_trend(unitid: str, trantype: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get daily trend data for last 30 days
        
        Args:
            unitid: Unit ID to filter data
            trantype: Optional transaction type filter
            
        Returns:
            List of daily trend data
        """
        query = """
        SELECT
            billdate AS day,
            trantype,
            COUNT(DISTINCT billno) AS bill_count,
            SUM(netamount) AS total_netamount
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        AND billdate >= today() - INTERVAL 30 DAY
        """
        
        parameters = {"unitid": unitid}
        
        if trantype:
            query += " AND trantype = {trantype:String}"
            parameters["trantype"] = trantype
            
        query += """
        GROUP BY 1, 2
        ORDER BY 1, 2
        """
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_daily_trend: {str(e)}")
            raise
    
    @staticmethod
    def get_mtd_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get MTD vs Last MTD comparison (same month, previous year)
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of MTD comparison data
        """
        query = """
        WITH current_mtd AS (
            SELECT
                'Current MTD' AS period,
                SUM(netamount) AS netamount
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            AND toMonth(billdate) = toMonth(today())
            AND toYear(billdate) = toYear(today())
            AND billdate <= today()
        ),
        last_year_mtd AS (
            SELECT
                'Last Year MTD' AS period,
                SUM(netamount) AS netamount
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            AND toMonth(billdate) = toMonth(today())
            AND toYear(billdate) = toYear(today()) - 1
            AND billdate <= (today() - INTERVAL 1 YEAR)
        )
        SELECT * FROM current_mtd
        UNION ALL
        SELECT * FROM last_year_mtd
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_mtd_comparison: {str(e)}")
            raise
    
    @staticmethod
    def get_ytd_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get YTD vs Last YTD comparison (Jan to Today)
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of YTD comparison data
        """
        query = """
        WITH current_ytd AS (
            SELECT
                'Current YTD' AS period,
                SUM(netamount) AS netamount
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            AND toYear(billdate) = toYear(today())
            AND billdate <= today()
        ),
        last_year_ytd AS (
            SELECT
                'Last Year YTD' AS period,
                SUM(netamount) AS netamount
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            AND toYear(billdate) = toYear(today()) - 1
            AND billdate <= (today() - INTERVAL 1 YEAR)
        )
        SELECT * FROM current_ytd
        UNION ALL
        SELECT * FROM last_year_ytd
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_ytd_comparison: {str(e)}")
            raise
    
    @staticmethod
    def get_yoy_comparison(unitid: str) -> List[Dict[str, Any]]:
        """
        Get Year-over-Year comparison (monthly)
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of YoY comparison data
        """
        query = """
        SELECT
            monthName(billdate) AS month_name,
            toYear(billdate) AS year,
            SUM(netamount) AS netamount
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        AND toYear(billdate) >= toYear(today()) - 2
        GROUP BY 1, 2
        ORDER BY 2, toMonth(billdate)
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_yoy_comparison: {str(e)}")
            raise
    
    @staticmethod
    def get_doctor_summary(unitid: str, start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get doctor-wise summary from materialized view
        
        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of doctor summary data
        """
        query = """
        SELECT 
            performingdoctor,
            performingdoctorspeciality,
            bill_month,
            netamount,
            totalamount,
            transaction_count
        FROM monthly_revenue_summary
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if start_date:
            query += " AND bill_month >= {start_date:String}"
            parameters["start_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            query += " AND bill_month <= {end_date:String}"
            parameters["end_date"] = end_date.strftime('%Y-%m-%d')
            
        query += " ORDER BY bill_month DESC, netamount DESC"
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_doctor_summary: {str(e)}")
            raise
    
    @staticmethod
    def get_service_summary(unitid: str, start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get service department summary from materialized view
        
        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of service summary data
        """
        query = """
        SELECT 
            service_department,
            bill_month,
            netamount,
            quantity,
            bill_count
        FROM monthly_revenue_summary
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if start_date:
            query += " AND bill_month >= {start_date:String}"
            parameters["start_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            query += " AND bill_month <= {end_date:String}"
            parameters["end_date"] = end_date.strftime('%Y-%m-%d')
            
        query += " ORDER BY bill_month DESC, netamount DESC"
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_service_summary: {str(e)}")
            raise
    
    @staticmethod
    def get_monthly_summary(unitid: str, start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get monthly summary from materialized view
        
        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of monthly summary data
        """
        query = """
        SELECT *
        FROM monthly_revenue_summary
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if start_date:
            query += " AND bill_month >= {start_date:String}"
            parameters["start_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            query += " AND bill_month <= {end_date:String}"
            parameters["end_date"] = end_date.strftime('%Y-%m-%d')
            
        query += " ORDER BY bill_month DESC"
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            return result
        except Exception as e:
            logger.error(f"Error in get_monthly_summary: {str(e)}")
            raise
