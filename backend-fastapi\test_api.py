#!/usr/bin/env python3
"""
Test API endpoints
"""
import requests
import json
import time

def test_api():
    base_url = "http://localhost:8000"
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(5)
    
    try:
        # Test root endpoint
        print("Testing root endpoint...")
        response = requests.get(f"{base_url}/")
        print(f"Root endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        
        # Test health endpoint
        print("\nTesting health endpoint...")
        response = requests.get(f"{base_url}/health")
        print(f"Health endpoint: {response.status_code}")
        if response.status_code == 200:
            print(f"Response: {response.json()}")
        
        # Test pie chart endpoint
        print("\nTesting pie chart endpoint...")
        data = {"unitid": "11"}
        response = requests.post(
            f"{base_url}/api/charts/pie-chart?chart_type=payertype",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Pie chart endpoint: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Pie chart working! Data count: {len(result.get('data', []))}")
            print(f"Total value: {result.get('total_value', 0):,.2f}")
        else:
            print(f"❌ Error: {response.text}")

        # Test gauge chart endpoint
        print("\nTesting gauge chart endpoint...")
        response = requests.post(
            f"{base_url}/api/charts/gauge-chart?target_mtd=2000000",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Gauge chart endpoint: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Gauge chart working! Current MTD: {result.get('current_mtd', 0):,.2f}")
        else:
            print(f"❌ Error: {response.text}")

        # Test treemap endpoint
        print("\nTesting treemap endpoint...")
        response = requests.post(
            f"{base_url}/api/charts/treemap",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Treemap endpoint: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Treemap working! Data count: {len(result.get('data', []))}")
        else:
            print(f"❌ Error: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on port 8000.")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_api()
