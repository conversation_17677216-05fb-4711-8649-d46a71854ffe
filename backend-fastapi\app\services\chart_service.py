"""
Chart Service for BI Dashboard Charts
Implements queries from BI Scripts for various chart types using ClickHouse
"""
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
import logging
from app.services.clickhouse_service import get_clickhouse_service

logger = logging.getLogger(__name__)

class ChartService:
    """Service for chart-specific analytics operations"""
    
    @staticmethod
    def get_pie_chart_data(unitid: str, chart_type: str = "payertype") -> Tuple[List[Dict[str, Any]], float]:
        """
        Get pie chart data for payertype or unit-wise share
        
        Args:
            unitid: Unit ID to filter data
            chart_type: Type of pie chart (payertype, unit, etc.)
            
        Returns:
            Tuple of (pie chart data, total value)
        """
        if chart_type == "payertype":
            query = """
            SELECT
                payertype AS category,
                SUM(netamount) AS value
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            GROUP BY 1
            ORDER BY 2 DESC
            """
        else:
            # Default to payertype if unknown type
            query = """
            SELECT
                payertype AS category,
                SUM(netamount) AS value
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            GROUP BY 1
            ORDER BY 2 DESC
            """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            raw_data = clickhouse_service.execute_query(query, parameters)

            # Calculate total and percentages
            total_value = sum(item['value'] for item in raw_data)

            # Add percentage calculation
            for item in raw_data:
                item['percentage'] = (item['value'] / total_value * 100) if total_value > 0 else 0

            return raw_data, total_value
        except Exception as e:
            logger.error(f"Error getting pie chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_treemap_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get treemap data for Service Department → Doctor → Net Revenue
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of treemap data
        """
        query = """
        SELECT
            COALESCE(service_department, 'Unknown') AS service_department,
            COALESCE(performingdoctor, 'Unknown') AS performingdoctor,
            SUM(netamount) AS netamount
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        AND service_department IS NOT NULL
        AND performingdoctor IS NOT NULL
        GROUP BY 1, 2
        ORDER BY 3 DESC
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting treemap data: {str(e)}")
            raise
    
    @staticmethod
    def get_scatter_plot_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get scatter plot data for Rate vs Quantity by Service
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of scatter plot data
        """
        query = """
        SELECT
            servicename AS service_name,
            AVG(rate) AS rate,
            SUM(quantity) AS quantity
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1
        ORDER BY 3 DESC
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting scatter plot data: {str(e)}")
            raise
    
    @staticmethod
    def get_histogram_data(unitid: str, bucket_count: int = 10, max_amount: float = 10000) -> List[Dict[str, Any]]:
        """
        Get histogram data for distribution of net amounts
        
        Args:
            unitid: Unit ID to filter data
            bucket_count: Number of buckets for histogram
            max_amount: Maximum amount to consider
            
        Returns:
            List of histogram data
        """
        query = """
        WITH buckets AS (
            SELECT
                floor(netamount / {bucket_size:Float64}) AS bucket,
                COUNT(*) AS frequency,
                floor(netamount / {bucket_size:Float64}) * {bucket_size:Float64} AS range_start,
                (floor(netamount / {bucket_size:Float64}) + 1) * {bucket_size:Float64} AS range_end
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            AND netamount <= {max_amount:Float64}
            GROUP BY 1
        )
        SELECT 
            bucket,
            frequency,
            range_start,
            range_end
        FROM buckets
        ORDER BY bucket
        """
        
        bucket_size = max_amount / bucket_count
        parameters = {
            "unitid": unitid,
            "bucket_size": bucket_size,
            "max_amount": max_amount
        }
        
        try:
            clickhouse_service = get_clickhouse_service()
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting histogram data: {str(e)}")
            raise
    
    @staticmethod
    def get_pareto_chart_data(unitid: str, limit: int = 20) -> Tuple[List[Dict[str, Any]], float]:
        """
        Get pareto chart data for top services contributing to revenue
        
        Args:
            unitid: Unit ID to filter data
            limit: Number of top services to return
            
        Returns:
            Tuple of (pareto chart data, total amount)
        """
        query = """
        WITH service_totals AS (
            SELECT
                servicename AS service_name,
                SUM(netamount) AS netamount
            FROM vw_opdrevenue
            WHERE unitid = {unitid:String}
            GROUP BY 1
            ORDER BY 2 DESC
            LIMIT {limit:UInt32}
        ),
        total_amount AS (
            SELECT SUM(netamount) AS total FROM service_totals
        )
        SELECT 
            service_name,
            netamount,
            SUM(netamount) OVER (ORDER BY netamount DESC) / (SELECT total FROM total_amount) * 100 AS cumulative_percentage
        FROM service_totals
        ORDER BY netamount DESC
        """
        
        parameters = {"unitid": unitid, "limit": limit}
        
        try:
            clickhouse_service = get_clickhouse_service()
            raw_data = clickhouse_service.execute_query(query, parameters)
            total_amount = sum(item['netamount'] for item in raw_data)
            return raw_data, total_amount
        except Exception as e:
            logger.error(f"Error getting pareto chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_gauge_chart_data(unitid: str, target_mtd: float = 2000000) -> Dict[str, Any]:
        """
        Get gauge chart data for current MTD vs target
        
        Args:
            unitid: Unit ID to filter data
            target_mtd: Target MTD amount
            
        Returns:
            Gauge chart data
        """
        query = """
        SELECT
            SUM(netamount) AS current_mtd
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        AND toMonth(billdate) = toMonth(today())
        AND toYear(billdate) = toYear(today())
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            result = clickhouse_service.execute_query(query, parameters)
            current_mtd = result[0]['current_mtd'] if result and result[0]['current_mtd'] is not None else 0
            percentage = (current_mtd / target_mtd * 100) if target_mtd > 0 and current_mtd is not None else 0

            return {
                "current_mtd": current_mtd,
                "target_mtd": target_mtd,
                "percentage": percentage
            }
        except Exception as e:
            logger.error(f"Error getting gauge chart data: {str(e)}")
            raise
    
    @staticmethod
    def get_heatmap_calendar_data(unitid: str, start_date: Optional[date] = None, end_date: Optional[date] = None) -> List[Dict[str, Any]]:
        """
        Get heatmap calendar data for daily net amounts
        
        Args:
            unitid: Unit ID to filter data
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            List of heatmap calendar data
        """
        query = """
        SELECT
            billdate AS date,
            SUM(netamount) AS netamount
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        """
        
        parameters = {"unitid": unitid}
        
        if start_date:
            query += " AND billdate >= {start_date:String}"
            parameters["start_date"] = start_date.strftime('%Y-%m-%d')
            
        if end_date:
            query += " AND billdate <= {end_date:String}"
            parameters["end_date"] = end_date.strftime('%Y-%m-%d')
        else:
            # Default to last 90 days if no end date specified
            query += " AND billdate >= today() - INTERVAL 90 DAY"
            
        query += """
        GROUP BY 1
        ORDER BY 1
        """
        
        try:
            clickhouse_service = get_clickhouse_service()
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting heatmap calendar data: {str(e)}")
            raise
    
    @staticmethod
    def get_sankey_chart_data(unitid: str) -> List[Dict[str, Any]]:
        """
        Get sankey chart data for patient type to speciality to service flow
        
        Args:
            unitid: Unit ID to filter data
            
        Returns:
            List of sankey chart data
        """
        query = """
        SELECT
            payertype AS source,
            performingdoctorspeciality AS target,
            COUNT(*) AS value
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1, 2

        UNION ALL

        SELECT
            performingdoctorspeciality AS source,
            service_department AS target,
            COUNT(*) AS value
        FROM vw_opdrevenue
        WHERE unitid = {unitid:String}
        GROUP BY 1, 2
        
        ORDER BY value DESC
        """
        
        parameters = {"unitid": unitid}
        
        try:
            clickhouse_service = get_clickhouse_service()
            return clickhouse_service.execute_query(query, parameters)
        except Exception as e:
            logger.error(f"Error getting sankey chart data: {str(e)}")
            raise
